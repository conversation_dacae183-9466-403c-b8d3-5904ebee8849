import { ChevronRightIcon } from '@shape-construction/arch-ui/src/Icons/outline';

type ChangeSignalWidgetProps = {
    onBrowseClick: () => void;
};
export const ChangeSignalWidget: React.FC<ChangeSignalWidgetProps> = ({ onBrowseClick }) => {
    return (
        <div className="flex items-stretch bg-brand-subtlest border border-brand-subtlest rounded-sm">
            {/* First section - Change signals */}
            <div className="flex items-center px-4 py-2">
                <span className="text-xs font-medium text-gray-900">
                    Change signals
                </span>
            </div>

            {/* Small separator between first and second section */}
            {/* <div className="flex items-center">
                <div className="w-px h-4 bg-gray-300" />
            </div> */}

            {/* Second section - Count and Unprocessed */}
            {/* <div className="flex items-center gap-2 px-4 py-2">
                <Badge size={SIZE.SMALL} label="10" shape={SHAPE.BASIC} theme={THEME.RED} />
                <span className="text-xs text-gray-700">
                    Unprocessed
                </span>
            </div> */}

            {/* Full height border between second and third section */}
            <div className="w-px bg-gray-300" />

            {/* Third section - Browse button */}
            <div className="flex items-center px-4 py-2">
                <button
                    onClick={onBrowseClick}
                    type="button"
                    className="flex gap-1 items-center text-xs font-medium text-brand"
                >
                    Browse
                    <ChevronRightIcon width={16} height={16} />
                </button>
            </div>
        </div>
    );
};