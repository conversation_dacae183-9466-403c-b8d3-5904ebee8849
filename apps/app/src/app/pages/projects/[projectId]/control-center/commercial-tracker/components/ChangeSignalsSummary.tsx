import { useMessageGetter } from '@messageformat/react';
import { Button, cn } from '@shape-construction/arch-ui';
import { PlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import React from 'react';

type ChangeSignalsSummaryProps = {
  totalChanges: number;
  isSelectionMode?: boolean;
  onNewChangeClick: () => void;
};

export const ChangeSignalsSummary: React.FC<ChangeSignalsSummaryProps> = ({ totalChanges, isSelectionMode, onNewChangeClick }) => {
  const messages = useMessageGetter('controlCenter.changeSignals');

  return (
    <div className="summary p-4 md:px-8 md:py-2 bg-neutral-subtlest border-y-1">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="summary-title flex items-center mr-4">
            <span
              className={cn('text-xs leading-4 font-medium text-neutral', { 'cursor-not-allowed opacity-50': isSelectionMode })}
            >
              {/* {messages('title')} */}
              Total entries: {totalChanges}
            </span>
          </div>
        </div>
        <div className="flex items-center gap-3">
          {/* <Button
            aria-label={messages('changeSignalsCTA')}
            size="xs"
            color="primary"
            variant="outlined"
            leadingIcon={ChevronDoubleLeftIcon}
            onClick={onOpenDrawer}
            disabled={isSelectionMode}
          >
            {messages('changeSignalsCTA')}
          </Button> */}
          <Button
            aria-label={messages('changeSignalsCTA')}
            size="xs"
            color="primary"
            variant="contained"
            leadingIcon={PlusIcon}
            onClick={onNewChangeClick}
            disabled={isSelectionMode}
          >
            New change
          </Button>
        </div>
      </div>
    </div>
  );
};
